const {restartServerSafely} = require('./serverActions');
const reloadServer = require('../../worker/reloadServer');

async function testSingleRestart() {
    console.log('=== Testing Single Server Restart ===');
    try {
        await main();
        console.log('✅ Single restart completed successfully');
    } catch (error) {
        console.error('❌ Single restart failed:', error);
    }
}

async function testParallelRestarts(count = 3) {
    console.log(`=== Testing ${count} Parallel Server Restarts ===`);
    const restarts = Array(count).fill().map(async (_, index) => {
        try {
            console.log(`Starting restart ${index + 1}`);
            await restartServerSafely();
            console.log(`✅ Restart ${index + 1} completed`);
        } catch (error) {
            console.error(`❌ Restart ${index + 1} failed:`, error);
        }
    });

    await Promise.all(restarts);
    console.log('All parallel restarts completed');
}

async function runTests() {
    // Test single restart first
   
    // Test parallel restarts
    await testParallelRestarts();
}

if (require.main === module) {
    runTests().catch(console.error);
}