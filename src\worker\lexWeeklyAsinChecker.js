const cron = require("node-cron");
const prisma = require("../database/prisma/getPrismaClient");
const { addToQueue } = require("../utils/bull/bull");
const { sendMessageToSlack } = require("../utils/slack");

// Singleton protection to prevent multiple cron job registrations
if (global.lexWeeklyAsinCheckerInitialized) {
  console.log("🔄 Lex Weekly ASIN Checker already initialized, skipping...");
  return;
}
global.lexWeeklyAsinCheckerInitialized = true;

const SLACK_WEBHOOK_URL = process.env.LEX_NOTI_SLACK_WEBHOOK_URL;

async function refetchClientAsins() {
  try {
    console.log("Job for Refetching CLIENT ASINs");

    const asinRecords = await prisma.lexASIN.findMany({
      where: {
        type: "CLIENT",
      },
    });

    const jobs = [];

    for (const asinRecord of asinRecords) {
      const job = await addToQueue(
        "singleLexAsin",
        {
          asin: asinRecord.id,
          countryCode: asinRecord.countryCode || "US",
        },
        {
          targetQueue: "lex",
        }
      );

      jobs.push({ asin: asinRecord.asin, jobId: job.id });
      console.log(`Queued Job for ${asinRecord.asin} → JobID: ${job.id}`);
    }

    console.log(`Total Jobs Queued: ${jobs.length}`);
  } catch (error) {
    console.error("Error in scheduled job:", error);
  }
}

if (process.env.SERVER_ID === "Main") {
  try {
    cron.schedule(
      "0 12 * * 6", // Every Saturday at 12 PM IST
      refetchClientAsins,
      {
        timezone: "Asia/Kolkata",
        scheduled: true,
        runOnInit: false,
      }
    );

    sendMessageToSlack(
      SLACK_WEBHOOK_URL,
      "Cron job for refetchClientAsins scheduled successfully (Runs every Saturday at 12 PM IST)"
    );
  } catch (err) {
    console.error("Failed to schedule cron job:", err.message);

    sendMessageToSlack(
      SLACK_WEBHOOK_URL,
      `Failed to schedule cron job for CLIENT's ASINs: ${err.message}`
    );
  }
}
