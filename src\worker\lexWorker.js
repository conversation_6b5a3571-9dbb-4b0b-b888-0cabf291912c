const prisma = require("../database/prisma/getPrismaClient");
const cron = require("node-cron");
const { JobStatus, ScrapingStatus } = require("@prisma/client");
const {
  scrapeAsinsFromSellerId,
} = require("../services/lex/scrapeAsinsFromSellerId");
const { scrapeDataFromAsins } = require("../services/lex/scrapeDataFromAsins");
require("dotenv").config();

// Global flags to track processing
let isProcessingJobs = false;

// Only start the worker if this is the Main server
// if (process.env.SERVER_ID === "Main") {
//   console.log("Starting LEX SellerId, ASIN Worker on Main server");

//   // Process jobs every 2 minutes
//   cron.schedule("*/2 * * * *", () => {
//     processLexJobs();
//   });

//   // Initial run
//   setTimeout(() => {
//     processLexJobs();
//   }, 5000); // Start after 5 seconds to avoid conflicts
// } else {
//   console.log("Not running LEX Worker - Not Main server");
// }

/**
 * Process pending LEX jobs based on type
 */
async function processLexJobs() {
  if (isProcessingJobs) {
    console.log("LEX jobs already being processed, skipping...");
    return;
  }

  try {
    isProcessingJobs = true;
    console.log("🔍 Processing LEX Jobs...");

    // Get all pending jobs
    const pendingJobs = await prisma.lexJob.findMany({
      where: {
        status: JobStatus.PENDING,
        type: {
          notIn: ["SINGLE_REVIEW", "BULK_REVIEW"],
        },
      },
      orderBy: { createdAt: "asc" },
      include: {
        asins: {
          where: { status: "PENDING" },
          select: {
            id: true,
            asin: true,
            status: true,
          },
        },
      },
    });

    if (!pendingJobs || pendingJobs.length === 0) {
      console.log("📭 No pending LEX jobs found SellerId & ASIN");
      return;
    }

    console.log(`🚀 Found ${pendingJobs.length} pending jobs to process`);

    // Group jobs by type
    const jobsByType = pendingJobs.reduce((acc, job) => {
      if (!acc[job.type]) {
        acc[job.type] = [];
      }
      acc[job.type].push(job);
      return acc;
    }, {});

    console.log(
      `📊 Job distribution:`,
      Object.keys(jobsByType)
        .map((type) => `${type}: ${jobsByType[type].length}`)
        .join(", ")
    );

    // Process each job type one by one
    for (const [jobType, jobs] of Object.entries(jobsByType)) {
      console.log(`\n🎯 Processing ${jobs.length} ${jobType} jobs...`);

      // Process each job in this type one by one
      for (let i = 0; i < jobs.length; i++) {
        const job = jobs[i];
        console.log(
          `📦 Processing ${jobType} job ${i + 1}/${jobs.length}: ${job.name}`
        );

        await processJobSafely(job);
      }

      console.log(`✅ Completed all ${jobs.length} ${jobType} jobs`);
    }

    console.log(`✅ Completed processing all ${pendingJobs.length} jobs`);
  } catch (error) {
    console.error("❌ Error in processLexJobs:", error);
  } finally {
    isProcessingJobs = false;
  }
}

/**
 * Safely process a single job with error handling
 */
async function processJobSafely(job) {
  try {
    console.log(`🚀 Starting ${job.type} Job: ${job.name} (ID: ${job.id})`);

    // Update job status to IN_PROGRESS
    await prisma.lexJob.update({
      where: { id: job.id },
      data: {
        status: JobStatus.IN_PROGRESS,
        updatedAt: new Date(),
      },
    });

    // Route to appropriate processor based on job type
    switch (job.type) {
      case "SELLER_ID":
        await processSellerJob(job);
        break;
      case "SINGLE_ASIN":
        await processSingleAsinJob(job);
        break;
      case "BULK_ASIN":
        await processBulkAsinJob(job);
        break;
      default:
        throw new Error(`Unknown job type: ${job.type}`);
    }

    console.log(`✅ Successfully completed ${job.type} Job: ${job.name}`);
  } catch (error) {
    console.error(`❌ Error processing ${job.type} Job ${job.name}:`, error);

    try {
      // Mark job as failed
      await prisma.lexJob.update({
        where: { id: job.id },
        data: {
          status: JobStatus.FAILED,
          errorMessage: error.message,
          updatedAt: new Date(),
        },
      });

      // Update related entities based on job type
      await handleJobFailure(job);
    } catch (updateError) {
      console.error(`❌ Error updating failed job ${job.id}:`, updateError);
    }
  }
}

/**
 * Process a seller ID job
 */
async function processSellerJob(job) {
  const { sellerId, countryCode = "US" } = job;

  console.log(`Starting scrape for seller: ${sellerId}`);

  // Fetch seller record (by internal DB id)
  let seller = await prisma.lexSeller.findUnique({ where: { id: sellerId } });

  if (!seller) {
    console.error(`❌ Seller with id ${sellerId} not found in DB.`);
    return;
  }

  const asinResults = await scrapeAsinsFromSellerId(
    seller.sellerId,
    countryCode
  );

  if (!asinResults || asinResults.length === 0) {
    console.log(`No ASINs found for seller: ${seller.sellerId}`);

    await prisma.lexSeller.update({
      where: { id: seller.id },
      data: { status: ScrapingStatus.SCRAPED, updatedAt: new Date() },
    });

    return;
  }

  const sellerName = asinResults[0]?.sellerName || null;
  const savedAsins = [];
  const failedAsins = [];

  for (const asinData of asinResults) {
    try {
      const savedAsin = await prisma.lexASIN.upsert({
        where: {
          asin_countryCode: {
            asin: asinData.asin,
            countryCode: countryCode,
          },
        },
        update: {
          title: asinData.title,
          image: asinData.image,
          productLink: asinData.productLink,
          avgRating: asinData.avgRating,
          totalReviews: asinData.totalReviews,
          sellerId: seller.sellerId,
          sellerName: asinData.sellerName,
          countryCode: countryCode,
          LexSellerId: seller.id,
          status: ScrapingStatus.SCRAPED,
          updatedAt: new Date(),
        },
        create: {
          asin: asinData.asin,
          title: asinData.title,
          image: asinData.image,
          productLink: asinData.productLink,
          avgRating: asinData.avgRating,
          totalReviews: asinData.totalReviews,
          sellerId: seller.sellerId,
          sellerName: asinData.sellerName,
          countryCode: countryCode,
          LexSellerId: seller.id,
          status: ScrapingStatus.SCRAPED,
        },
      });
      savedAsins.push(savedAsin);
    } catch (err) {
      console.error(`❌ Failed to save ASIN ${asinData.asin}:`, err.message);
      failedAsins.push(asinData.asin);
    }
  }

  await prisma.lexSeller.update({
    where: { id: seller.id },
    data: {
      status: ScrapingStatus.SCRAPED,
      updatedAt: new Date(),
      name: sellerName,
    },
  });

  console.log(
    `Scraping completed for seller: ${seller.sellerId}, saved: ${savedAsins.length}, failed: ${failedAsins.length}`
  );

  return {
    sellerId: seller.sellerId,
    savedCount: savedAsins.length,
    failedCount: failedAsins.length,
    failedAsins,
  };
}
/**
 * Process a single ASIN job
 */
async function processSingleAsinJob(job) {
  console.log(`🕷️ Scraping data for single ASIN: ${job.asin}`);

  const targetCountryCode = job.countryCode || "US";

  // Update ASIN status to IN_PROGRESS using compound unique key
  await prisma.lexASIN.update({
    where: {
      asin_countryCode: {
        asin: job.asin,
        countryCode: targetCountryCode,
      },
    },
    data: {
      status: ScrapingStatus.IN_PROGRESS,
      updatedAt: new Date(),
    },
  });

  const asinResults = await scrapeDataFromAsins([job.asin], targetCountryCode);

  if (!asinResults || asinResults.length === 0) {
    await prisma.lexASIN.update({
      where: {
        asin_countryCode: {
          asin: job.asin,
          countryCode: targetCountryCode,
        },
      },
      data: {
        status: ScrapingStatus.FAILED,
        updatedAt: new Date(),
      },
    });
    throw new Error("No data found for ASIN");
  }

  const asinData = asinResults[0];
  if (asinData.error) {
    await prisma.lexASIN.update({
      where: {
        asin_countryCode: {
          asin: job.asin,
          countryCode: targetCountryCode,
        },
      },
      data: {
        status: ScrapingStatus.FAILED,
        updatedAt: new Date(),
      },
    });
    throw new Error(`Scraping failed: ${asinData.error}`);
  }

  // Update seller if we have seller info and seller exists
  let sellerId = null;
  if (asinData.sellerId) {
    try {
      const seller = await prisma.lexSeller.upsert({
        where: { sellerId: asinData.sellerId },
        update: {
          name: asinData.sellerName,
          countryCode: targetCountryCode,
          status: ScrapingStatus.SCRAPED,
          updatedAt: new Date(),
        },
        create: {
          sellerId: asinData.sellerId,
          name: asinData.sellerName,
          countryCode: targetCountryCode,
          status: ScrapingStatus.SCRAPED,
        },
      });
      sellerId = seller.id;
    } catch (sellerError) {
      console.error(`Warning: Could not update seller: ${sellerError.message}`);
    }
  }

  // Get existing values
  const existingAsin = await prisma.lexASIN.findUnique({
    where: {
      asin_countryCode: {
        asin: asinData.asin,
        countryCode: targetCountryCode,
      },
    },
    select: {
      avgRating: true,
      totalReviews: true,
      reviewCounts: true,
    },
  });

  const hasExistingData =
    existingAsin?.avgRating !== null ||
    existingAsin?.totalReviews !== null ||
    existingAsin?.reviewCounts !== null;

  // Build update object
  const updateData = {
    title: asinData.title,
    sellerName: asinData.sellerName,
    sellerId: asinData.sellerId,
    countryCode: targetCountryCode,
    category: asinData.category,
    image: asinData.image,
    productLink: asinData.productLink,
    LexSellerId: sellerId,
    status: ScrapingStatus.SCRAPED,
    updatedAt: new Date(),
    ...(hasExistingData
      ? {
          latestData: {
            avgRating: asinData.avgRating,
            totalReviews: asinData.totalReviews,
            reviewCounts: asinData.reviewCounts,
          },
        }
      : {
          avgRating: asinData.avgRating,
          totalReviews: asinData.totalReviews,
          reviewCounts: asinData.reviewCounts,
        }),
  };

  const savedAsin = await prisma.lexASIN.upsert({
    where: {
      asin_countryCode: {
        asin: asinData.asin,
        countryCode: targetCountryCode,
      },
    },
    update: updateData,
    create: {
      asin: asinData.asin,
      title: asinData.title,
      sellerName: asinData.sellerName,
      sellerId: asinData.sellerId,
      countryCode: targetCountryCode,
      category: asinData.category,
      image: asinData.image,
      productLink: asinData.productLink,
      avgRating: asinData.avgRating,
      totalReviews: asinData.totalReviews,
      reviewCounts: asinData.reviewCounts,
      LexSellerId: sellerId,
      status: ScrapingStatus.SCRAPED,
    },
  });

  console.log(`✅ Scraped ASIN ${savedAsin.asin}`);
}

/**
 * Process a bulk ASIN job
 */
async function processBulkAsinJob(job) {
  console.log(`🕷️ Processing bulk ASIN job: ${job.name}`);

  if (!job.asins || job.asins.length === 0) {
    console.log(
      `ℹ️ No pending ASINs found for bulk job ${job.name} - likely already processed`
    );

    // Mark job as completed since there's nothing to process
    await prisma.lexJob.update({
      where: { id: job.id },
      data: {
        status: JobStatus.COMPLETED,
        errorMessage: null,
        updatedAt: new Date(),
      },
    });

    console.log(
      `✅ Bulk ASIN Job ${job.name} completed - no pending ASINs to process`
    );
    return;
  }

  const asinsToScrape = job.asins.map((asin) => asin.asin);
  console.log(`📦 Found ${asinsToScrape.length} ASINs to scrape in bulk job`);

  // Update all ASINs status to PENDING using transaction for better performance
  const updatePromises = asinsToScrape.map((asin) =>
    prisma.lexASIN
      .updateMany({
        where: {
          asin: asin,
          countryCode: job.countryCode || "US",
        },
        data: {
          status: ScrapingStatus.PENDING,
          updatedAt: new Date(),
        },
      })
      .catch((error) => {
        console.error(
          `❌ Failed to update ASIN ${asin} to PENDING:`,
          error.message
        );
        return null;
      })
  );

  await Promise.all(updatePromises);

  // Scrape all ASINs
  const asinResults = await scrapeDataFromAsins(asinsToScrape, job.countryCode);

  let successCount = 0;
  let failCount = 0;

  // Process each ASIN result one by one
  for (const asinData of asinResults) {
    try {
      if (asinData.error) {
        console.error(
          `❌ Error scraping ASIN ${asinData.asin}: ${asinData.error}`
        );

        await prisma.lexASIN.updateMany({
          where: {
            asin: asinData.asin,
            countryCode: job.countryCode || "US",
          },
          data: {
            status: ScrapingStatus.FAILED,
            updatedAt: new Date(),
          },
        });

        failCount++;
        continue;
      }

      // Update seller if we have seller info and seller exists
      let sellerId = null;
      if (asinData.sellerId) {
        try {
          const existingSeller = await prisma.lexSeller.findUnique({
            where: { sellerId: asinData.sellerId },
          });

          if (existingSeller) {
            const updatedSeller = await prisma.lexSeller.update({
              where: { sellerId: asinData.sellerId },
              data: {
                name: asinData.sellerName,
                countryCode: job.countryCode,
                status: ScrapingStatus.SCRAPED,
                updatedAt: new Date(),
              },
            });
            sellerId = updatedSeller.id;
          }
        } catch (sellerError) {
          console.error(
            `Warning: Could not update seller for ASIN ${asinData.asin}: ${sellerError.message}`
          );
        }
      }

      await prisma.lexASIN.upsert({
        where: {
          asin_countryCode: {
            asin: asinData.asin,
            countryCode: job.countryCode || "US",
          },
        },
        update: {
          title: asinData.title,
          sellerName: asinData.sellerName,
          sellerId: asinData.sellerId,
          countryCode: job.countryCode,
          category: asinData.category,
          image: asinData.image,
          productLink: asinData.productLink,
          avgRating: asinData.avgRating,
          totalReviews: asinData.totalReviews,
          reviewCounts: asinData.reviewCounts,
          LexSellerId: sellerId,
          status: ScrapingStatus.SCRAPED,
          updatedAt: new Date(),
        },
        create: {
          asin: asinData.asin,
          title: asinData.title,
          sellerName: asinData.sellerName,
          sellerId: asinData.sellerId,
          countryCode: job.countryCode,
          category: asinData.category,
          image: asinData.image,
          productLink: asinData.productLink,
          avgRating: asinData.avgRating,
          totalReviews: asinData.totalReviews,
          reviewCounts: asinData.reviewCounts,
          LexSellerId: sellerId,
          status: ScrapingStatus.SCRAPED,
        },
      });

      successCount++;
      console.log(
        `✅ Successfully scraped ASIN: ${asinData.asin} - Seller: ${asinData.sellerName}`
      );
    } catch (error) {
      console.error(`❌ Error saving ASIN ${asinData.asin}:`, error);

      await prisma.lexASIN.updateMany({
        where: {
          asin: asinData.asin,
          countryCode: job.countryCode || "US",
        },
        data: {
          status: ScrapingStatus.FAILED,
          updatedAt: new Date(),
        },
      });

      failCount++;
    }
  }

  // Mark bulk job as completed
  await prisma.lexJob.update({
    where: { id: job.id },
    data: {
      status: JobStatus.COMPLETED,
      errorMessage:
        failCount > 0 ? `Failed to scrape ${failCount} ASINs` : null,
      updatedAt: new Date(),
    },
  });

  console.log(`✅ Bulk ASIN Job ${job.name} completed!`);
  console.log(`   📊 Total ASINs: ${asinsToScrape.length}`);
  console.log(`   ✅ Successfully scraped: ${successCount}`);
  console.log(`   ❌ Failed: ${failCount}`);
}

/**
 * Handle job failure by updating related entities
 */
async function handleJobFailure(job) {
  try {
    switch (job.type) {
      case "SELLER_ID":
        if (job.sellerId) {
          await prisma.lexSeller.updateMany({
            where: { sellerId: job.sellerId },
            data: {
              status: ScrapingStatus.FAILED,
              updatedAt: new Date(),
            },
          });
        }
        break;
      case "SINGLE_ASIN":
        if (job.asin) {
          await prisma.lexASIN.updateMany({
            where: { asin: job.asin },
            data: {
              status: ScrapingStatus.FAILED,
              updatedAt: new Date(),
            },
          });
        }
        break;
      case "BULK_ASIN":
        if (job.asins && job.asins.length > 0) {
          await prisma.lexASIN.updateMany({
            where: {
              asin: { in: job.asins.map((a) => a.asin) },
              status: ScrapingStatus.PENDING,
            },
            data: {
              status: ScrapingStatus.FAILED,
              updatedAt: new Date(),
            },
          });
        }
        break;
    }
  } catch (error) {
    console.error("Error handling job failure:", error);
  }
}

// Graceful shutdown
process.on("SIGINT", () => {
  console.log("🛑 LEX Worker shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("🛑 LEX Worker shutting down gracefully...");
  process.exit(0);
});

module.exports = {
  processLexJobs,
  processSellerJob,
  processSingleAsinJob,
  processBulkAsinJob,
};
