const { Worker, QueueEvents } = require("bullmq");
const { JobStatus } = require("@prisma/client");
const prisma = require("../database/prisma/getPrismaClient");
const jobHandlers = require("../utils/bull/jobHandlers");
require("dotenv").config();
const { connection } = require("../utils/bull/connection"); // connection exported from your bull.js

// Singleton protection to prevent multiple worker instances
if (global.bullWorkerInitialized) {
  console.log("🔄 Bull worker already initialized, skipping...");
  return;
}
global.bullWorkerInitialized = true;

// Helper for timestamped logs
const log = (msg, data = null) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${msg}`);
  if (data) console.log(data);
};

// Determine which queue this server should process
const queueName = process.env.SERVER_QUEUE || "lexQueue";

log(`🔧 Initializing worker for queue: '${queueName}'`);

const worker = new Worker(
  queueName,
  async (job) => {
    const { id: jobId } = job.data;
    log(`⚙️ Processing job → ID: ${jobId}, Type: ${job.name}`);

    try {
      await prisma.jobCentral.update({
        where: { id: jobId },
        data: { status: JobStatus.IN_PROGRESS },
      });

      const handler = jobHandlers[job.name];
      if (!handler) throw new Error(`No handler for job type ${job.name}`);

      await handler(job.data);

      await prisma.jobCentral.update({
        where: { id: jobId },
        data: { status: JobStatus.COMPLETED },
      });
    } catch (err) {
      log(`❌ Error processing job ID: ${jobId}`, err);
      await prisma.jobCentral.update({
        where: { id: jobId },
        data: { status: JobStatus.FAILED },
      });
      throw err;
    }
  },
  { connection }
);

// Worker lifecycle events
worker.on("ready", () => {
  log(`✅ Worker connected and ready (queue: '${queueName}')`);
});

worker.on("error", (err) => {
  log(`❌ Worker error: ${err.message}`);
});

// QueueEvents (logs job lifecycle)
const queueEvents = new QueueEvents(queueName, { connection });

queueEvents.on("waiting", ({ jobId }) => {
  log(`📥 Job waiting → ID: ${jobId}`);
});

queueEvents.on("active", ({ jobId }) => {
  log(`🚀 Job started → ID: ${jobId}`);
});

queueEvents.on("completed", ({ jobId }) => {
  log(`✅ Job completed → ID: ${jobId}`);
});

queueEvents.on("failed", ({ jobId, failedReason }) => {
  log(`❌ Job failed → ID: ${jobId}, Reason: ${failedReason}`);
});
