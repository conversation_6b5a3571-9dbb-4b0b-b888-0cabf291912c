const express = require("express");
const prisma = require("../database/prisma/getPrismaClient");
const { addToQueue } = require("../utils/bull/bull");
const multer = require("multer");
const csv = require("csv-parser");
const fs = require("fs");
const path = require("path");
const {
  cleanGuidelineLookupText,
} = require("../utils/lexUtils/cleanGuidelineLookupText");

// Configure multer for CSV uploads
const upload = multer({
  dest: "uploads/csv/",
  fileFilter: (req, file, cb) => {
    if (file.mimetype === "text/csv" || file.originalname.endsWith(".csv")) {
      cb(null, true);
    } else {
      cb(new Error("Only CSV files are allowed"), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

const router = express.Router();

// GET /api/lex_prompts - Get all prompt chains
router.get("/api/lex_prompts", async (req, res) => {
  try {
    const { active_only = false, limit = 50 } = req.query;

    const whereClause = active_only === "true" ? { isActive: true } : {};

    const promptChains = await prisma.lexPromptChain.findMany({
      where: whereClause,
      orderBy: { updatedAt: "desc" },
      take: parseInt(limit),
    });

    res.json({
      success: true,
      data: promptChains,
      count: promptChains.length,
    });
  } catch (error) {
    console.error("Error fetching prompt chains:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch prompt chains",
      details: error.message,
    });
  }
});

// GET /api/lex_prompts/models - Get available AI models
router.get("/api/lex_prompts/models", async (req, res) => {
  try {
    const { MultiProviderAI } = require("../services/ai/multiProviderAI");
    const aiService = new MultiProviderAI();
    const models = aiService.getAvailableModels();

    res.json({
      success: true,
      data: models,
      message: "Available AI models for prompt chain execution",
    });
  } catch (error) {
    console.error("Error fetching available models:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch available models",
      details: error.message,
    });
  }
});

// GET /api/lex_prompts/variables - Get available variables for prompt templates
router.get("/api/lex_prompts/variables", async (req, res) => {
  try {
    const availableVariables = [
      { name: "reviewContent", description: "The content/text of the review" },
      { name: "reviewTitle", description: "The title of the review" },
      {
        name: "productTitle",
        description: "The title of the product being reviewed",
      },
      { name: "productLink", description: "Link to the product page" },
      {
        name: "reviewScore",
        description: "Rating score given by the reviewer",
      },
      { name: "reviewer", description: "Name of the reviewer" },
      { name: "reviewerCountry", description: "Country of the reviewer" },
      { name: "asin", description: "Amazon Standard Identification Number" },
      { name: "sellerId", description: "ID of the seller" },
      { name: "reviewDate", description: "Date when the review was posted" },
      { name: "variant_0", description: "Product variant information" },
      {
        name: "variant_1",
        description: "Additional product variant information",
      },
    ];

    res.json({
      success: true,
      data: availableVariables,
      message:
        "Use {{variableName}} in your prompts to replace with actual values",
    });
  } catch (error) {
    console.error("Error getting available variables:", error);
    res.status(500).json({
      success: false,
      error: "Failed to get available variables",
      details: error.message,
    });
  }
});

// GET /api/lex_prompts/bulk-jobs - Get all lex prompt chain jobs (bulk and single) with download capability
router.get("/api/lex_prompts/bulk-jobs", async (req, res) => {
  try {
    const { status, limit = 50, offset = 0 } = req.query;

    // Include both bulk and single lex prompt chain jobs
    const whereClause = {
      scriptType: {
        in: [
          "bulkLexPromptChain",
          "singleLexPromptChain",
          "singleLexViolationDetection",
          "bulkLexViolationDetection",
        ],
      },
    };

    if (status) {
      whereClause.status = status;
    }

    const allJobs = await prisma.jobCentral.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      take: parseInt(limit),
      skip: parseInt(offset),
    });

    const jobsWithSummary = allJobs.map((job) => {
      const csvMetadata = job.params?.csvInputMetadata || {};
      const outputMetadata = job.params?.outputMetadata || {};
      const reviewsData = outputMetadata.results || [];

      // Get review count based on job type
      let reviewCount = 0;
      if (job.scriptType === "bulkLexPromptChain") {
        reviewCount =
          job.params?.reviewIds?.length || csvMetadata.processedReviews || 0;
      } else if (job.scriptType === "singleLexPromptChain") {
        reviewCount = job.params?.reviewId ? 1 : 0;
      } else if (job.scriptType.includes("Violation")) {
        reviewCount =
          job.params?.reviewIds?.length || (job.params?.reviewId ? 1 : 0);
      }

      return {
        id: job.id,
        type: job.scriptType,
        status: job.status,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        promptChainId: job.params?.promptChainId,
        model: job.params?.model,
        isClient: job.params?.isClient,
        promptType: job.params?.promptType || "execution",
        summary: {
          fileName: csvMetadata.fileName || "N/A",
          totalRows: csvMetadata.totalRows || reviewCount,
          reviewCount: reviewCount,
          processingErrors: csvMetadata.processingErrors?.length || 0,
          totalCost: job.cost || outputMetadata.performance?.totalCost || 0,
          successfulReviews:
            outputMetadata.bulkSummary?.successfulReviews ||
            (job.status === "COMPLETED" ? reviewCount : 0),
          failedReviews: outputMetadata.bulkSummary?.failedReviews || 0,
        },
        hasResults: job.status === "COMPLETED",
        canDownload:
          job.status === "COMPLETED" &&
          (reviewsData.length > 0 || reviewCount > 0),
      };
    });

    res.json({
      success: true,
      data: jobsWithSummary,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: jobsWithSummary.length,
      },
      message: "All lex prompt chain jobs retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching lex prompt chain jobs:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch lex prompt chain jobs",
      details: error.message,
    });
  }
});

// GET /api/lex_prompts/jobs/:jobId - Get job status
router.get("/api/lex_prompts/jobs/:jobId", async (req, res) => {
  try {
    const { jobId } = req.params;

    const job = await prisma.jobCentral.findUnique({
      where: { id: parseInt(jobId) },
    });

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    // Check if it's a prompt chain job
    if (!job.scriptType.includes("PromptChain")) {
      return res.status(400).json({
        success: false,
        error: "Job is not a prompt chain execution job",
      });
    }

    res.json({
      success: true,
      data: {
        id: job.id,
        type: job.scriptType,
        status: job.status,
        priority: job.priority,
        queueName: job.queueName,
        params: job.params,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
      },
    });
  } catch (error) {
    console.error("Error fetching job status:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch job status",
      details: error.message,
    });
  }
});

// GET /api/lex_prompts/:id - Get specific prompt chain
router.get("/api/lex_prompts/:id", async (req, res) => {
  try {
    const { id } = req.params;
    if (!id || isNaN(id)) {
      return res.status(404).json({
        success: false,
        error: "Prompt chain not found",
      });
    }
    const promptChain = await prisma.lexPromptChain.findUnique({
      where: { id: parseInt(id) },
    });

    if (!promptChain) {
      return res.status(404).json({
        success: false,
        error: "Prompt chain not found",
      });
    }

    res.json({
      success: true,
      data: promptChain,
    });
  } catch (error) {
    console.error("Error fetching prompt chain:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch prompt chain",
      details: error.message,
    });
  }
});

// POST /api/lex_prompts - Create new prompt chain
router.post("/api/lex_prompts", async (req, res) => {
  try {
    const {
      name,
      description,
      prompt1,
      prompt2,
      prompt3,
      model = "azure-gpt4o",
      isActive = true,
      isPrimary = false,
      isClient = false,
    } = req.body;

    // Validate required fields
    if (!name || !prompt1 || !prompt2 || !prompt3) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields: name, prompt1, prompt2, prompt3",
      });
    }

    // Validate model selection
    const { MultiProviderAI } = require("../services/ai/multiProviderAI");
    const aiService = new MultiProviderAI();
    const availableModels = aiService.getAvailableModels();
    const validModels = availableModels.map((m) => m.id);

    if (!validModels.includes(model)) {
      return res.status(400).json({
        success: false,
        error: `Invalid model. Must be one of: ${validModels.join(", ")}`,
        availableModels: availableModels,
      });
    }

    // If setting as primary, first unset any existing primary prompt chain
    if (isPrimary) {
      await prisma.lexPromptChain.updateMany({
        where: { isPrimary: true },
        data: { isPrimary: false },
      });
    }

    let promptChain;

    // Create new prompt chain
    promptChain = await prisma.lexPromptChain.create({
      data: {
        name,
        description,
        prompt1,
        prompt2,
        prompt3,
        model,
        isActive,
        isPrimary,
        isClient,
      },
    });

    res.status(201).json({
      success: true,
      data: promptChain,
      message: "Prompt chain created successfully",
    });
  } catch (error) {
    console.error("Error creating/updating prompt chain:", error);
    res.status(500).json({
      success: false,
      error: "Failed to create/update prompt chain",
      details: error.message,
    });
  }
});

// PUT /api/lex_prompts/:id - Update prompt chain
router.put("/api/lex_prompts/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      prompt1,
      prompt2,
      prompt3,
      model,
      isActive,
      isPrimary,
      isClient,
    } = req.body;

    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (prompt1 !== undefined) updateData.prompt1 = prompt1;
    if (prompt2 !== undefined) updateData.prompt2 = prompt2;
    if (prompt3 !== undefined) updateData.prompt3 = prompt3;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (isPrimary !== undefined) updateData.isPrimary = isPrimary;
    if (isClient !== undefined) updateData.isClient = isClient;

    // Validate model if provided
    if (model !== undefined) {
      const { MultiProviderAI } = require("../services/ai/multiProviderAI");
      const aiService = new MultiProviderAI();
      const availableModels = aiService.getAvailableModels();
      const validModels = availableModels.map((m) => m.id);

      if (!validModels.includes(model)) {
        return res.status(400).json({
          success: false,
          error: `Invalid model. Must be one of: ${validModels.join(", ")}`,
          availableModels: availableModels,
        });
      }
      updateData.model = model;
    }

    // If setting as primary, first unset any existing primary prompt chain
    if (isPrimary) {
      await prisma.lexPromptChain.updateMany({
        where: {
          isPrimary: true,
          id: { not: parseInt(id) },
        },
        data: { isPrimary: false },
      });
    }

    const promptChain = await prisma.lexPromptChain.update({
      where: { id: parseInt(id) },
      data: updateData,
    });

    res.json({
      success: true,
      data: promptChain,
      message: "Prompt chain updated successfully",
    });
  } catch (error) {
    console.error("Error updating prompt chain:", error);

    if (error.code === "P2025") {
      return res.status(404).json({
        success: false,
        error: "Prompt chain not found",
      });
    }

    res.status(500).json({
      success: false,
      error: "Failed to update prompt chain",
      details: error.message,
    });
  }
});

// DELETE /api/lex_prompts/:id - Delete prompt chain
router.delete("/api/lex_prompts/:id", async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.lexPromptChain.delete({
      where: { id: parseInt(id) },
    });

    res.json({
      success: true,
      message: "Prompt chain deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting prompt chain:", error);

    if (error.code === "P2025") {
      return res.status(404).json({
        success: false,
        error: "Prompt chain not found",
      });
    }

    res.status(500).json({
      success: false,
      error: "Failed to delete prompt chain",
      details: error.message,
    });
  }
});

// POST /api/lex_prompts/:id/execute - Queue prompt chain execution (prompts 1 and 2 only)
router.post("/api/lex_prompts/:id/execute", async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewId, reviewIds, model } = req.body;

    // Get the prompt chain
    const promptChain = await prisma.lexPromptChain.findUnique({
      where: { id: parseInt(id) },
    });

    if (!promptChain) {
      return res.status(404).json({
        success: false,
        error: "Prompt chain not found",
      });
    }

    if (!promptChain.isActive) {
      return res.status(400).json({
        success: false,
        error: "Prompt chain is not active",
      });
    }

    // Model validation if provided, otherwise use model from prompt chain
    let executionModel = promptChain.model;
    if (model) {
      const { MultiProviderAI } = require("../services/ai/multiProviderAI");
      const aiService = new MultiProviderAI();
      const availableModels = aiService.getAvailableModels();
      const validModels = availableModels.map((m) => m.id);

      if (!validModels.includes(model)) {
        return res.status(400).json({
          success: false,
          error: `Invalid model. Must be one of: ${validModels.join(", ")}`,
          availableModels: availableModels,
        });
      }
      executionModel = model;
    }

    let targetReviewIds = [];

    // Handle multiple review IDs
    if (reviewIds && Array.isArray(reviewIds)) {
      const reviews = await prisma.lexReview.findMany({
        where: { id: { in: reviewIds.map((id) => parseInt(id)) } },
        select: { id: true },
      });

      if (reviews.length === 0) {
        return res.status(404).json({
          success: false,
          error: "No reviews found for provided IDs",
        });
      }

      targetReviewIds = reviews.map((r) => r.id);
    }
    // Handle single review ID
    else if (reviewId) {
      const review = await prisma.lexReview.findUnique({
        where: { id: parseInt(reviewId) },
        select: { id: true },
      });

      if (!review) {
        return res.status(404).json({
          success: false,
          error: "Review not found",
        });
      }

      targetReviewIds = [review.id];
    }

    if (targetReviewIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Either reviewId or reviewIds array must be provided",
      });
    }

    // Queue job for prompts 1 and 2 only
    let bullJob;
    if (targetReviewIds.length === 1) {
      const jobData = {
        promptChainId: parseInt(id),
        reviewId: targetReviewIds[0],
        promptType: "execution", // Only run prompts 1 and 2
        model: executionModel,
      };

      bullJob = await addToQueue("singleLexPromptChain", jobData, {
        targetQueue: "lex",
      });
    } else {
      const jobData = {
        promptChainId: parseInt(id),
        reviewIds: targetReviewIds,
        promptType: "execution", // Only run prompts 1 and 2
        model: executionModel,
      };

      bullJob = await addToQueue("bulkLexPromptChain", jobData, {
        targetQueue: "lex",
      });
    }

    console.log(
      `✅ Prompt chain execution (prompts 1 & 2) queued for ${targetReviewIds.length} review(s) using ${executionModel}`
    );

    res.json({
      success: true,
      data: {
        promptChain: {
          id: promptChain.id,
          name: promptChain.name,
        },
        job: {
          id: bullJob.id,
          type:
            targetReviewIds.length === 1
              ? "singleLexPromptChain"
              : "bulkLexPromptChain",
          status: "PENDING",
        },
        reviewIds: targetReviewIds,
        totalReviews: targetReviewIds.length,
        promptType: "execution",
        model: executionModel,
      },
      message: "Prompt chain execution (prompts 1 & 2) queued successfully",
    });
  } catch (error) {
    console.error("Error queueing prompt chain execution:", error);
    res.status(500).json({
      success: false,
      error: "Failed to queue prompt chain execution",
      details: error.message,
    });
  }
});

// POST /api/lex_prompts/execute - Queue primary prompt chain execution (prompts 1 and 2 only)
router.post("/api/lex_prompts/execute", async (req, res) => {
  try {
    const { reviewId, reviewIds, model } = req.body;

    // Get the primary prompt chain
    const promptChain = await prisma.lexPromptChain.findFirst({
      where: {
        isPrimary: true,
        isActive: true,
      },
    });

    if (!promptChain) {
      return res.status(404).json({
        success: false,
        error:
          "No primary prompt chain found. Please set a prompt chain as primary first.",
      });
    }

    // Model validation if provided, otherwise use model from prompt chain
    let executionModel = promptChain.model;
    if (model) {
      const { MultiProviderAI } = require("../services/ai/multiProviderAI");
      const aiService = new MultiProviderAI();
      const availableModels = aiService.getAvailableModels();
      const validModels = availableModels.map((m) => m.id);

      if (!validModels.includes(model)) {
        return res.status(400).json({
          success: false,
          error: `Invalid model. Must be one of: ${validModels.join(", ")}`,
          availableModels: availableModels,
        });
      }
      executionModel = model;
    }

    let targetReviewIds = [];

    // Handle multiple review IDs
    if (reviewIds && Array.isArray(reviewIds)) {
      const reviews = await prisma.lexReview.findMany({
        where: { id: { in: reviewIds.map((id) => parseInt(id)) } },
        select: { id: true },
      });

      if (reviews.length === 0) {
        return res.status(404).json({
          success: false,
          error: "No reviews found for provided IDs",
        });
      }

      targetReviewIds = reviews.map((r) => r.id);
    }
    // Handle single review ID
    else if (reviewId) {
      const review = await prisma.lexReview.findUnique({
        where: { id: parseInt(reviewId) },
        select: { id: true },
      });

      if (!review) {
        return res.status(404).json({
          success: false,
          error: "Review not found",
        });
      }

      targetReviewIds = [review.id];
    }

    if (targetReviewIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Either reviewId or reviewIds array must be provided",
      });
    }

    // Queue job for prompts 1 and 2 only
    let bullJob;
    if (targetReviewIds.length === 1) {
      const jobData = {
        promptChainId: promptChain.id,
        reviewId: targetReviewIds[0],
        promptType: "execution", // Only run prompts 1 and 2
        model: executionModel,
      };

      bullJob = await addToQueue("singleLexPromptChain", jobData, {
        targetQueue: "lex",
      });
    } else {
      const jobData = {
        promptChainId: promptChain.id,
        reviewIds: targetReviewIds,
        promptType: "execution", // Only run prompts 1 and 2
        model: executionModel,
      };

      bullJob = await addToQueue("bulkLexPromptChain", jobData, {
        targetQueue: "lex",
      });
    }

    console.log(
      `✅ Primary prompt chain execution (prompts 1 & 2) queued for ${targetReviewIds.length} review(s) using ${executionModel}`
    );

    res.json({
      success: true,
      data: {
        promptChain: {
          id: promptChain.id,
          name: promptChain.name,
          isPrimary: true,
        },
        job: {
          id: bullJob.id,
          type:
            targetReviewIds.length === 1
              ? "singleLexPromptChain"
              : "bulkLexPromptChain",
          status: "PENDING",
        },
        reviewIds: targetReviewIds,
        totalReviews: targetReviewIds.length,
        promptType: "execution",
        model: executionModel,
      },
      message:
        "Primary prompt chain execution (prompts 1 & 2) queued successfully",
    });
  } catch (error) {
    console.error("Error queueing primary prompt chain execution:", error);
    res.status(500).json({
      success: false,
      error: "Failed to queue primary prompt chain execution",
      details: error.message,
    });
  }
});

// POST /api/lex_prompts/run-violation - Run prompt 3 only (violation detection)
router.post("/api/lex_prompts/run-violation", async (req, res) => {
  try {
    const { reviewIds, model = "azure-gpt4o" } = req.body;

    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "reviewIds array is required and must not be empty",
      });
    }

    // --- Model Validation ---
    const { MultiProviderAI } = require("../services/ai/multiProviderAI");
    const aiService = new MultiProviderAI();
    const availableModels = aiService.getAvailableModels();
    const validModels = availableModels.map((m) => m.id);

    if (!validModels.includes(model)) {
      return res.status(400).json({
        success: false,
        error: `Invalid model. Must be one of: ${validModels.join(", ")}`,
      });
    }

    // --- Fetch Reviews and Last Prompt Chain ---
    const reviews = await prisma.lexReview.findMany({
      where: { id: { in: reviewIds.map((id) => parseInt(id)) } },
      select: { id: true, lastPromptChainId: true },
    });

    if (reviews.length === 0) {
      return res.status(404).json({
        success: false,
        error: "No reviews found for provided IDs",
      });
    }

    const reviewWithoutPromptChain = reviews.find((r) => !r.lastPromptChainId);
    if (reviewWithoutPromptChain) {
      return res.status(400).json({
        success: false,
        error: `Review with ID ${reviewWithoutPromptChain.id} has not been processed by a prompt chain yet.`,
      });
    }

    // --- Queue Violation Detection Jobs ---
    const jobPromises = reviews.map((review) => {
      const jobData = {
        reviewId: review.id,
        promptChainId: review.lastPromptChainId,
        model,
        promptType: "violation", // Only run prompt 3
      };
      return addToQueue("singleLexViolationDetection", jobData, {
        targetQueue: "lex",
      });
    });

    const jobs = await Promise.all(jobPromises);

    console.log(
      `✅ Violation detection queued for ${reviews.length} review(s) using ${model}`
    );

    res.json({
      success: true,
      data: {
        jobs: jobs.map((j) => ({ id: j.id, type: j.name, status: "PENDING" })),
        totalReviews: reviews.length,
        model,
        promptType: "violation",
      },
      message: "Violation detection jobs queued successfully",
    });
  } catch (error) {
    console.error("Error queueing violation detection:", error);
    res.status(500).json({
      success: false,
      error: "Failed to queue violation detection job",
      details: error.message,
    });
  }
});

// POST /api/lex_prompts/run-integrated - Run full chain (prompts 1, 2, and 3)
router.post("/api/lex_prompts/run-integrated", async (req, res) => {
  try {
    const {
      reviewIds,
      promptChainId,
      isClient,
      model, // Optional: to override the chain's default model
    } = req.body;

    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "reviewIds array is required and must not be empty",
      });
    }

    // --- Model Validation ---
    const { MultiProviderAI } = require("../services/ai/multiProviderAI");
    const aiService = new MultiProviderAI();
    const availableModels = aiService.getAvailableModels();
    console.log(availableModels, "availableModels");
    const validModels = availableModels.map((m) => m.id);

    if (model && !validModels.includes(model)) {
      return res.status(400).json({
        success: false,
        error: `Invalid model. Must be one of: ${validModels.join(", ")}`,
      });
    }

    // --- Get Prompt Chain ---
    let promptChain;
    if (promptChainId) {
      // Use specific prompt chain
      promptChain = await prisma.lexPromptChain.findUnique({
        where: { id: parseInt(promptChainId) },
      });
      if (!promptChain) {
        return res.status(404).json({
          success: false,
          error: "Prompt chain not found",
        });
      }
    } else if (isClient !== undefined) {
      // Auto-select based on isClient flag
      promptChain = await prisma.lexPromptChain.findFirst({
        where: {
          isClient: isClient,
          isActive: true,
        },
      });
      if (!promptChain) {
        return res.status(404).json({
          success: false,
          error: `No active prompt chain found for isClient=${isClient}`,
        });
      }
    } else {
      // Use primary prompt chain as fallback
      promptChain = await prisma.lexPromptChain.findFirst({
        where: { isPrimary: true, isActive: true },
      });
      if (!promptChain) {
        return res.status(404).json({
          success: false,
          error: "No primary prompt chain found and no isClient flag provided",
        });
      }
    }

    if (!promptChain.isActive) {
      return res.status(400).json({
        success: false,
        error: "Selected prompt chain is not active",
      });
    }

    const executionModel = model || promptChain.model;
    console.log(reviewIds, model, "model");
    // --- Validate and Update Reviews ---
    const reviews = await prisma.lexReview.findMany({
      where: { reviewID: { in: reviewIds } },
      select: { id: true },
    });

    if (reviews.length === 0) {
      return res.status(404).json({
        success: false,
        error: "No reviews found for provided IDs",
      });
    }

    const validReviewIds = reviews.map((r) => r.id);

    await prisma.lexReview.updateMany({
      where: { id: { in: validReviewIds } },
      data: {
        isClient: promptChain.isClient, // Auto-set from prompt chain
        lastPromptChainId: promptChain.id, // Store the last used prompt chain ID
        status: "AI_ANALYSIS_PENDING", // Set initial status
      },
    });

    // --- Queue Jobs ---
    let bullJob;
    if (validReviewIds.length === 1) {
      const jobData = {
        reviewId: validReviewIds[0],
        promptChainId: promptChain.id,
        model: executionModel,
        promptType: "execution",
      };
      bullJob = await addToQueue("singleLexPromptChain", jobData, {
        targetQueue: "lex",
      });
    } else {
      const jobData = {
        reviewIds: validReviewIds,
        promptChainId: promptChain.id,
        model: executionModel,
        promptType: "execution",
      };
      bullJob = await addToQueue("bulkLexPromptChain", jobData, {
        targetQueue: "lex",
      });
    }

    console.log(
      `✅ Prompt chain execution queued for ${validReviewIds.length} review(s) using ${executionModel}`
    );

    res.json({
      success: true,
      data: {
        job: {
          id: bullJob.id,
          type: bullJob.name,
          status: "PENDING",
        },
        reviewIds: validReviewIds,
        totalReviews: validReviewIds.length,
        promptChain: {
          id: promptChain.id,
          name: promptChain.name,
          isClient: promptChain.isClient,
        },
        model: executionModel,
        promptType: "execution",
      },
      message: "Prompt chain execution queued successfully",
    });
  } catch (error) {
    console.error("Error queueing prompt chain execution:", error);
    res.status(500).json({
      success: false,
      error: "Failed to queue prompt chain execution job",
      details: error.message,
    });
  }
});

// POST /api/lex_prompts/upload-csv - Upload CSV with review data and process with existing prompt chains
router.post(
  "/api/lex_prompts/upload-csv",
  upload.single("csvFile"),
  async (req, res) => {
    try {
      const { file } = req;
      const { model } = req.body; // Optional model override

      if (!file) {
        return res.status(400).json({
          success: false,
          error: "No CSV file uploaded. Use 'csvFile' as the field name.",
        });
      }

      // Validate model if provided
      if (model) {
        const { MultiProviderAI } = require("../services/ai/multiProviderAI");
        const aiService = new MultiProviderAI();
        const availableModels = aiService.getAvailableModels();
        const validModels = availableModels.map((m) => m.id);

        if (!validModels.includes(model)) {
          return res.status(400).json({
            success: false,
            error: `Invalid model. Must be one of: ${validModels.join(", ")}`,
          });
        }
      }

      console.log(`📄 Processing CSV file: ${file.originalname}`);

      const csvData = [];
      const processingErrors = [];

      // Parse CSV and collect all rows
      await new Promise((resolve, reject) => {
        fs.createReadStream(file.path)
          .pipe(csv())
          .on("data", (row) => {
            csvData.push(row);
          })
          .on("end", resolve)
          .on("error", reject);
      });

      console.log(`📊 Parsed ${csvData.length} rows from CSV`);

      if (csvData.length === 0) {
        fs.unlinkSync(file.path);
        return res.status(400).json({
          success: false,
          error: "CSV file is empty or has no valid data rows",
        });
      }

      // Process CSV rows and prepare data for job metadata (DO NOT create database records)
      const processedReviews = [];
      const clientReviews = [];
      const nonClientReviews = [];

      for (let index = 0; index < csvData.length; index++) {
        const row = csvData[index];
        try {
          // Use the comprehensive header mapping function
          const reviewData = mapCsvRowToReviewData(row, index);

          // Validate required fields
          if (!reviewData.reviewID || !reviewData.reviewContent) {
            processingErrors.push({
              row: index + 1,
              error: "Missing required fields: reviewID and reviewContent",
            });
            continue;
          }

          // Add processed review data to memory arrays (NOT to database)
          processedReviews.push(reviewData);

          // Group by isClient flag for bulk processing
          if (reviewData.isClient) {
            clientReviews.push(reviewData);
          } else {
            nonClientReviews.push(reviewData);
          }
        } catch (error) {
          processingErrors.push({
            row: index + 1,
            error: error.message,
          });
        }
      }

      console.log(
        `🔄 Processed ${processedReviews.length} reviews: ${clientReviews.length} client, ${nonClientReviews.length} non-client`
      );

      // Get the existing prompt chains
      const clientPromptChain = await prisma.lexPromptChain.findFirst({
        where: { isClient: true, isActive: true },
      });

      const nonClientPromptChain = await prisma.lexPromptChain.findFirst({
        where: { isClient: false, isActive: true },
      });

      if (!clientPromptChain && clientReviews.length > 0) {
        processingErrors.push({
          error:
            "No active prompt chain found for client reviews (isClient=true)",
        });
      }

      if (!nonClientPromptChain && nonClientReviews.length > 0) {
        processingErrors.push({
          error:
            "No active prompt chain found for non-client reviews (isClient=false)",
        });
      }

      const jobs = [];
      const csvInputMetadata = {
        fileName: file.originalname,
        uploadedAt: new Date().toISOString(),
        totalRows: csvData.length,
        processedReviews: processedReviews.length,
        clientReviews: clientReviews.length,
        nonClientReviews: nonClientReviews.length,
        processingErrors: processingErrors,
        // Store the actual review data arrays instead of undefined IDs
        clientReviewsData: clientReviews,
        nonClientReviewsData: nonClientReviews,
      };

      // Create bulk job for client reviews
      if (clientReviews.length > 0 && clientPromptChain) {
        const jobData = {
          reviewIds: clientReviews.map((r) => r.reviewID),
          promptChainId: clientPromptChain.id,
          model: model || clientPromptChain.model,
          promptType: "csv_bulk_execution",
          csvMetadata: csvInputMetadata,
          isClient: true,
        };

        const clientJob = await addToQueue("bulkLexPromptChain", jobData, {
          targetQueue: "lex",
        });

        // Update job metadata to include CSV input data
        await prisma.jobCentral.update({
          where: { id: clientJob.id },
          data: {
            params: {
              ...clientJob.params,
              csvInputMetadata: csvInputMetadata,
            },
          },
        });

        jobs.push({
          id: clientJob.id,
          type: "bulkLexPromptChain",
          isClient: true,
          reviewCount: clientReviews.length,
          promptChainId: clientPromptChain.id,
          promptChainName: clientPromptChain.name,
        });
      }

      // Create bulk job for non-client reviews
      if (nonClientReviews.length > 0 && nonClientPromptChain) {
        const jobData = {
          reviewIds: nonClientReviews.map((r) => r.reviewID),
          promptChainId: nonClientPromptChain.id,
          model: model || nonClientPromptChain.model,
          promptType: "csv_bulk_execution",
          csvMetadata: csvInputMetadata,
          isClient: false,
        };

        const nonClientJob = await addToQueue("bulkLexPromptChain", jobData, {
          targetQueue: "lex",
        });

        // Update job metadata to include CSV input data
        await prisma.jobCentral.update({
          where: { id: nonClientJob.id },
          data: {
            params: {
              ...nonClientJob.params,
              csvInputMetadata: csvInputMetadata,
            },
          },
        });

        jobs.push({
          id: nonClientJob.id,
          type: "bulkLexPromptChain",
          isClient: false,
          reviewCount: nonClientReviews.length,
          promptChainId: nonClientPromptChain.id,
          promptChainName: nonClientPromptChain.name,
        });
      }

      // Clean up uploaded file
      fs.unlinkSync(file.path);

      console.log(
        `✅ CSV processing completed. Created ${processedReviews.length} review records and ${jobs.length} bulk job(s)`
      );

      res.json({
        success: true,
        data: {
          jobs: jobs,
          summary: {
            totalRows: csvData.length,
            processedReviews: processedReviews.length,
            clientReviews: clientReviews.length,
            nonClientReviews: nonClientReviews.length,
            jobsCreated: jobs.length,
            processingErrors: processingErrors,
          },
          csvMetadata: csvInputMetadata,
        },
        message:
          "CSV uploaded, reviews processed, and bulk jobs created successfully",
      });
    } catch (error) {
      console.error("Error processing CSV upload:", error);

      // Clean up file if it exists
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        success: false,
        error: "Failed to process CSV upload",
        details: error.message,
      });
    }
  }
);

// GET /api/lex_prompts/bulk-jobs - Get all lex prompt chain jobs (bulk and single) with download capability
router.get("/api/lex_prompts/bulk-jobs", async (req, res) => {
  try {
    const { status, limit = 50, offset = 0 } = req.query;

    // Include both bulk and single lex prompt chain jobs
    const whereClause = {
      scriptType: {
        in: [
          "bulkLexPromptChain",
          "singleLexPromptChain",
          "singleLexViolationDetection",
          "bulkLexViolationDetection",
        ],
      },
    };

    if (status) {
      whereClause.status = status;
    }

    const allJobs = await prisma.jobCentral.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      take: parseInt(limit),
      skip: parseInt(offset),
    });

    const jobsWithSummary = allJobs.map((job) => {
      const csvMetadata = job.params?.csvInputMetadata || {};
      const outputMetadata = job.params?.outputMetadata || {};
      const reviewsData = outputMetadata.results || [];

      // Get review count based on job type
      let reviewCount = 0;
      if (job.scriptType === "bulkLexPromptChain") {
        reviewCount =
          job.params?.reviewIds?.length || csvMetadata.processedReviews || 0;
      } else if (job.scriptType === "singleLexPromptChain") {
        reviewCount = job.params?.reviewId ? 1 : 0;
      } else if (job.scriptType.includes("Violation")) {
        reviewCount =
          job.params?.reviewIds?.length || (job.params?.reviewId ? 1 : 0);
      }

      return {
        id: job.id,
        type: job.scriptType,
        status: job.status,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        promptChainId: job.params?.promptChainId,
        model: job.params?.model,
        isClient: job.params?.isClient,
        promptType: job.params?.promptType || "execution",
        summary: {
          fileName: csvMetadata.fileName || "N/A",
          totalRows: csvMetadata.totalRows || reviewCount,
          reviewCount: reviewCount,
          processingErrors: csvMetadata.processingErrors?.length || 0,
          totalCost: job.cost || outputMetadata.performance?.totalCost || 0,
          successfulReviews:
            outputMetadata.bulkSummary?.successfulReviews ||
            (job.status === "COMPLETED" ? reviewCount : 0),
          failedReviews: outputMetadata.bulkSummary?.failedReviews || 0,
        },
        hasResults: job.status === "COMPLETED",
        canDownload:
          job.status === "COMPLETED" &&
          (reviewsData.length > 0 || reviewCount > 0),
      };
    });

    res.json({
      success: true,
      data: jobsWithSummary,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: jobsWithSummary.length,
      },
      message: "All lex prompt chain jobs retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching lex prompt chain jobs:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch lex prompt chain jobs",
      details: error.message,
    });
  }
});

// GET /api/lex_prompts/bulk-jobs/:jobId/download - Download results as CSV
router.get("/api/lex_prompts/bulk-jobs/:jobId/download", async (req, res) => {
  try {
    const { jobId } = req.params;

    const job = await prisma.jobCentral.findUnique({
      where: { id: parseInt(jobId) },
    });

    if (!job) {
      return res.status(404).json({
        success: false,
        error: "Job not found",
      });
    }

    if (!job.scriptType.includes("LexPromptChain")) {
      return res.status(400).json({
        success: false,
        error: "Job is not a prompt chain job",
      });
    }

    if (job.status !== "COMPLETED") {
      return res.status(400).json({
        success: false,
        error: "Job is not completed yet",
      });
    }
    console.log(job);
    // Get results from the correct location in outputMetadata
    const outputMetadata = job.params?.outputMetadata || {};
    const csvInputMetadata = job.params?.csvMetadata || {};
    const resultsData = outputMetadata.results || [];

    console.log(`📊 Job ${jobId} download attempt:`, {
      hasOutputMetadata: !!outputMetadata,
      hasResults: resultsData.length > 0,
      resultCount: resultsData.length,
      hasCsvMetadata: !!csvInputMetadata,
      jobType: job.scriptType,
    });

    // For CSV jobs, get the source data and merge with results
    let reviewsDataForDownload = [];

    if (
      csvInputMetadata &&
      (csvInputMetadata.clientReviewsData ||
        csvInputMetadata.nonClientReviewsData)
    ) {
      // This is a CSV job - get the original input data
      const isClientJob = job.params?.isClient;
      let sourceReviews = [];

      if (isClientJob) {
        sourceReviews = csvInputMetadata.clientReviewsData || [];
      } else {
        sourceReviews = csvInputMetadata.nonClientReviewsData || [];
      }

      // If no source reviews in csvInputMetadata, try getting from csvSourceData in outputMetadata
      if (sourceReviews.length === 0 && outputMetadata.csvSourceData) {
        sourceReviews = outputMetadata.csvSourceData || [];
      }

      console.log(
        `📄 CSV Job detected - using ${
          isClientJob ? "client" : "non-client"
        } reviews:`,
        sourceReviews.length
      );

      // Merge source data with results data
      reviewsDataForDownload = sourceReviews.map((sourceReview) => {
        const resultData =
          resultsData.find((r) => r.reviewId === sourceReview.reviewID) || {};

        return {
          // Original CSV data
          ...sourceReview,
          // AI processing results
          prompt1Output:
            resultData.outputs?.prompt1Output ||
            sourceReview.prompt1Output ||
            "",
          prompt2Output:
            resultData.outputs?.prompt2Output ||
            sourceReview.prompt2Output ||
            "",
          prompt3Output:
            resultData.outputs?.prompt3Output ||
            sourceReview.prompt3Output ||
            "",
          // Add the new fields
          prompt1Input:
            resultData.inputs?.prompt1Input || sourceReview.prompt1Input || "",
          prompt2Input:
            resultData.inputs?.prompt2Input || sourceReview.prompt2Input || "",
          prompt3Input:
            resultData.inputs?.prompt3Input || sourceReview.prompt3Input || "",
          // Top violations data
          guidelineViolation1:
            resultData.topViolations?.GuidelineViolation1 ||
            sourceReview.guidelineViolation1 ||
            "",
          guidelineViolation2:
            resultData.topViolations?.GuidelineViolation2 ||
            sourceReview.guidelineViolation2 ||
            "",
          guidelineViolation3:
            resultData.topViolations?.GuidelineViolation3 ||
            sourceReview.guidelineViolation3 ||
            "",
          confidenceScore1:
            resultData.topViolations?.ConfidenceScore1 ||
            sourceReview.confidenceScore1 ||
            0,
          confidenceScore2:
            resultData.topViolations?.ConfidenceScore2 ||
            sourceReview.confidenceScore2 ||
            0,
          confidenceScore3:
            resultData.topViolations?.ConfidenceScore3 ||
            sourceReview.confidenceScore3 ||
            0,
          reason1:
            resultData.topViolations?.Reason1 || sourceReview.reason1 || "",
          reason2:
            resultData.topViolations?.Reason2 || sourceReview.reason2 || "",
          reason3:
            resultData.topViolations?.Reason3 || sourceReview.reason3 || "",
          // Guideline lookup text
          guidelineViolation1LookupText: cleanGuidelineLookupText(
            resultData.guidelineViolation1LookupText ||
              sourceReview.guidelineViolation1LookupText ||
              ""
          ),
          violation:
            resultData.violationData?.violation ??
            sourceReview.violation ??
            false,
          violationConfidence:
            resultData.violationData?.confidence ??
            sourceReview.violationConfidence ??
            0,
          violationReason:
            resultData.violationData?.reason ||
            sourceReview.violationReason ||
            "",
          processingStatus: resultData.success ? "COMPLETED" : "FAILED",
          processingCost: resultData.cost?.totalCost || 0,
          processingTokens: resultData.tokenUsage?.totalTokens || 0,
          inputCost: resultData.cost?.inputCost || 0,
          outputCost: resultData.cost?.outputCost || 0,
          totalCost: resultData.cost?.totalCost || 0,
          promptTokens: resultData.tokenUsage?.promptTokens || 0,
          completionTokens: resultData.tokenUsage?.completionTokens || 0,
          totalTokens: resultData.tokenUsage?.totalTokens || 0,
        };
      });
    }

    if (reviewsDataForDownload.length === 0) {
      return res.status(404).json({
        success: false,
        error: "No reviews data available for download",
      });
    }

    // Create CSV content with comprehensive headers including new fields
    const csvHeaders = [
      // Original input columns
      "reviewID",
      "reviewContent",
      "reviewTitle",
      "productTitle",
      "productLink",
      "reviewScore",
      "reviewer",
      "reviewerCountry",
      "asin",
      "sellerId",
      "reviewDate",
      "variant_0",
      "variant_1",
      "isClient",
      // AI processing output columns
      "prompt1Output",
      "prompt2Output",
      "prompt3Output",
      // AI processing input columns (new)
      "prompt1Input",
      "prompt2Input",
      "prompt3Input",
      // Top violations data (new)
      "guidelineViolation1",
      "guidelineViolation2",
      "guidelineViolation3",
      "confidenceScore1",
      "confidenceScore2",
      "confidenceScore3",
      "reason1",
      "reason2",
      "reason3",
      "guidelineViolation1LookupText",
      // Existing violation fields
      "violation",
      "violationConfidence",
      "violationReason",
      "processingStatus",
      "processingCost",
      "processingTokens",
      "processingDate",
      "inputCost",
      "outputCost",
      "totalCost",
      "promptTokens",
      "completionTokens",
      "totalTokens",
    ];

    let csvContent = csvHeaders.join(",") + "\n";

    reviewsDataForDownload.forEach((reviewData) => {
      const row = [
        `"${reviewData.reviewID || ""}"`,
        `"${(reviewData.reviewContent || "").replace(/"/g, '""')}"`,
        `"${(reviewData.reviewTitle || "").replace(/"/g, '""')}"`,
        `"${(reviewData.productTitle || "").replace(/"/g, '""')}"`,
        `"${reviewData.productLink || ""}"`,
        `"${reviewData.reviewScore || ""}"`,
        `"${reviewData.reviewer || ""}"`,
        `"${reviewData.reviewerCountry || ""}"`,
        `"${reviewData.asin || ""}"`,
        `"${reviewData.sellerId || ""}"`,
        `"${reviewData.reviewDate || ""}"`,
        `"${reviewData.variant_0 || ""}"`,
        `"${reviewData.variant_1 || ""}"`,
        `"${reviewData.isClient}"`,
        // AI outputs
        `"${(reviewData.prompt1Output || "").replace(/"/g, '""')}"`,
        `"${(reviewData.prompt2Output || "").replace(/"/g, '""')}"`,
        `"${(reviewData.prompt3Output || "").replace(/"/g, '""')}"`,
        // AI inputs (new)
        `"${(reviewData.prompt1Input || "").replace(/"/g, '""')}"`,
        `"${(reviewData.prompt2Input || "").replace(/"/g, '""')}"`,
        `"${(reviewData.prompt3Input || "").replace(/"/g, '""')}"`,
        // Top violations (new)
        `"${reviewData.guidelineViolation1 || ""}"`,
        `"${reviewData.guidelineViolation2 || ""}"`,
        `"${reviewData.guidelineViolation3 || ""}"`,
        `"${reviewData.confidenceScore1 || 0}"`,
        `"${reviewData.confidenceScore2 || 0}"`,
        `"${reviewData.confidenceScore3 || 0}"`,
        `"${(reviewData.reason1 || "").replace(/"/g, '""')}"`,
        `"${(reviewData.reason2 || "").replace(/"/g, '""')}"`,
        `"${(reviewData.reason3 || "").replace(/"/g, '""')}"`,
        `"${(reviewData.guidelineViolation1LookupText || "").replace(
          /"/g,
          '""'
        )}"`,
        // Existing violation fields
        `"${reviewData.violation || false}"`,
        `"${reviewData.violationConfidence || 0}"`,
        `"${(reviewData.violationReason || "").replace(/"/g, '""')}"`,
        `"${reviewData.processingStatus || "UNKNOWN"}"`,
        `"${reviewData.processingCost || 0}"`,
        `"${reviewData.processingTokens || 0}"`,
        `"${new Date().toISOString()}"`,
        `"${reviewData.inputCost || 0}"`,
        `"${reviewData.outputCost || 0}"`,
        `"${reviewData.totalCost || 0}"`,
        `"${reviewData.promptTokens || 0}"`,
        `"${reviewData.completionTokens || 0}"`,
        `"${reviewData.totalTokens || 0}"`,
      ];
      csvContent += row.join(",") + "\n";
    });

    const fileName = `prompt_chain_job_${jobId}_results_${
      new Date().toISOString().split("T")[0]
    }.csv`;

    res.setHeader("Content-Type", "text/csv");
    res.setHeader("Content-Disposition", `attachment; filename="${fileName}"`);
    res.send(csvContent);

    console.log(
      `📥 Downloaded results for job ${jobId}: ${reviewsDataForDownload.length} rows`
    );
  } catch (error) {
    console.error("Error downloading job results:", error);
    res.status(500).json({
      success: false,
      error: "Failed to download results",
      details: error.message,
    });
  }
});

/**
 * Comprehensive CSV header mapping function that supports multiple formats
 * and is case-insensitive
 */
function mapCsvRowToReviewData(row, index) {
  // Create a case-insensitive key lookup
  const lowerCaseRow = {};
  Object.keys(row).forEach((key) => {
    lowerCaseRow[key.toLowerCase().replace(/\s+/g, "")] = row[key];
  });

  // Helper function to get value by trying multiple header variations
  function getValue(variations) {
    for (const variation of variations) {
      const key = variation.toLowerCase().replace(/\s+/g, "");
      if (
        lowerCaseRow[key] !== undefined &&
        lowerCaseRow[key] !== null &&
        lowerCaseRow[key] !== ""
      ) {
        return lowerCaseRow[key];
      }
    }
    return null;
  }

  // Extract isClient flag with multiple format support
  const isClientValue = getValue([
    "isClient",
    "IsClient",
    "is_client",
    "is client",
    "client",
  ]);
  const isClient =
    isClientValue === "true" ||
    isClientValue === true ||
    isClientValue === "1" ||
    isClientValue === 1;

  // Map all possible header variations to review data structure
  const reviewData = {
    // Review ID variations
    reviewID: getValue([
      "Review ID",
      "ReviewID",
      "review_id",
      "reviewid",
      "id",
      "ID",
    ]),

    // Review content variations
    reviewContent: getValue([
      "ReviewContent",
      "Review Content",
      "review_content",
      "reviewcontent",
      "content",
      "Content",
    ]),

    // Review title variations
    reviewTitle: getValue([
      "Review Title",
      "ReviewTitle",
      "review_title",
      "reviewtitle",
      "title",
      "Title",
    ]),

    // Product title variations
    productTitle: getValue([
      "ProductTitle",
      "Product Title",
      "product_title",
      "producttitle",
      "ProductName",
      "Product Name",
    ]),

    // Product link variations
    productLink: getValue([
      "ProductLink",
      "Product Link",
      "product_link",
      "productlink",
      "url",
      "URL",
      "Page URL",
      "PageURL",
    ]),

    // Review score variations
    reviewScore: getValue([
      "ReviewScore",
      "Review Score",
      "review_score",
      "reviewscore",
      "rating",
      "Rating",
      "score",
      "Score",
    ]),

    // Reviewer variations
    reviewer: getValue([
      "Reviewer",
      "reviewer",
      "ReviewerName",
      "Reviewer Name",
      "reviewer_name",
      "reviewername",
    ]),

    // Reviewer country variations
    reviewerCountry: getValue([
      "ReviewerCountry",
      "Reviewer Country",
      "reviewer_country",
      "reviewercountry",
      "country",
      "Country",
    ]),

    // ASIN variations
    asin: getValue([
      "ASIN",
      "asin",
      "Asin",
      "product_id",
      "ProductID",
      "Product ID",
    ]),

    // Seller ID variations
    sellerId: getValue([
      "Seller ID",
      "SellerID",
      "seller_id",
      "sellerid",
      "SellerId",
      "Seller Name",
      "SellerName",
    ]),

    // Review date variations
    reviewDate: getValue([
      "ReviewDate",
      "Review Date",
      "review_date",
      "reviewdate",
      "date",
      "Date",
      "Created At",
      "CreatedAt",
    ]),

    // Variant variations
    variant_0: getValue([
      "variant_0",
      "Variant_0",
      "variant0",
      "Variant0",
      "Variants",
      "variants",
      "variant",
      "Variant",
    ]),

    variant_1: getValue(["variant_1", "Variant_1", "variant1", "Variant1"]),

    // Additional fields from new format
    countryCode: getValue([
      "ASIN CountryCode",
      "ASINCountryCode",
      "asin_country_code",
      "countrycode",
      "country_code",
    ]),

    sellerName: getValue([
      "Seller Name",
      "SellerName",
      "seller_name",
      "sellername",
    ]),

    reviewerLink: getValue([
      "ReviewerLink",
      "Reviewer Link",
      "reviewer_link",
      "reviewerlink",
    ]),

    helpfulCounts: getValue([
      "HelpfulCounts",
      "Helpful Counts",
      "helpful_counts",
      "helpfulcounts",
      "helpful",
      "Helpful",
    ]),

    isVerified: getValue([
      "isVerified",
      "IsVerified",
      "is_verified",
      "isverified",
      "verified",
      "Verified",
    ]),

    reviewURL: getValue(["Review URL", "ReviewURL", "review_url", "reviewurl"]),

    images: getValue(["Images", "images", "image", "Image"]),

    // Pre-existing prompt outputs (if any)
    prompt1Output: getValue([
      "Prompt1 Output",
      "Prompt1Output",
      "prompt1_output",
      "prompt1output",
    ]),

    prompt2Output: getValue([
      "Prompt2 Output",
      "Prompt2Output",
      "prompt2_output",
      "prompt2output",
    ]),

    prompt3Output: getValue([
      "Prompt3 Output",
      "Prompt3Output",
      "prompt3_output",
      "prompt3output",
    ]),

    // Pre-existing violation data (if any)
    violation: getValue([
      "Violation",
      "violation",
      "GuidelineViolation1",
      "guideline_violation_1",
    ]),

    violationConfidence: getValue([
      "ConfidenceScore1",
      "Confidence Score 1",
      "confidence_score_1",
      "confidencescore1",
      "violationConfidence",
      "violation_confidence",
    ]),

    violationReason: getValue([
      "Reason1",
      "reason1",
      "violationReason",
      "violation_reason",
    ]),

    reviewStatus: getValue([
      "ReviewStatus",
      "Review Status",
      "review_status",
      "reviewstatus",
      "status",
      "Status",
    ]),

    // Set additional metadata
    isClient: isClient,
    status: "CSV_UPLOADED", // Mark as uploaded from CSV
  };

  return reviewData;
}

module.exports = router;
