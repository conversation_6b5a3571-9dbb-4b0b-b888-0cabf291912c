const { PrismaClient } = require("@prisma/client");
const {
  seedAmazonProductDataToProduct,
  migrateSpecificRecords,
  transformAmazonDataToProduct,
} = require("./amazonProductDataToProductSeeder");
const {
  testConnection,
  checkTargetTable,
} = require("./amazonProductDataToProductConfig");

const prisma = new PrismaClient();

// Test script for AmazonProductData to Product migration
async function testMigration() {
  try {
    console.log('=== AmazonProductData to Product Migration Test ===\n');

    // Test 1: Database Connection
    console.log('1. Testing database connection...');
    const connectionOk = await testConnection();
    if (!connectionOk) {
      console.error('❌ Database connection failed');
      return false;
    }
    console.log('✅ Database connection successful\n');

    // Test 2: Target Table Check
    console.log('2. Checking target table...');
    const tableExists = await checkTargetTable();
    if (!tableExists) {
      console.error('❌ Target table "products" does not exist');
      return false;
    }
    console.log('✅ Target table exists\n');

    // Test 3: Source Data Check
    console.log('3. Checking source data...');
    const totalRecords = await prisma.amazonProductData.count();
    console.log(`✅ Found ${totalRecords} AmazonProductData records`);

    if (totalRecords === 0) {
      console.log('⚠️  No AmazonProductData records found to migrate');
      return false;
    }

    // Test 4: Sample Data Transformation
    console.log('\n4. Testing data transformation...');
    const sampleRecord = await prisma.amazonProductData.findFirst({
      where: {
        id: 82729
     }
    });

    if (!sampleRecord) {
      console.error('❌ No sample record found');
      return false;
    }

    console.log(`✅ Sample record found (ID: ${sampleRecord.id})`);

    const transformedProduct = transformAmazonDataToProduct(sampleRecord);
    console.log('✅ Data transformation successful');
    console.log('Sample transformed data:');
    console.log('- URL:', transformedProduct.url);
    console.log('- Brand:', transformedProduct.brand_name);
    console.log('- Title:', transformedProduct.product_title?.substring(0, 50) + '...');
    console.log('- Price:', transformedProduct.price);
    console.log('- Rating:', transformedProduct.rating);
    console.log('- Reviews:', transformedProduct.total_reviews);

    // Test 5: Migration with Sample Data
    console.log('\n5. Testing migration with sample data...');
    try {
      await migrateSpecificRecords([sampleRecord.id]);
      console.log('✅ Sample migration successful');
    } catch (error) {
      console.error('❌ Sample migration failed:', error.message);
      return false;
    }

    console.log('\n=== All Tests Passed ===');
    console.log('✅ The migration system is ready to use');
    console.log('\nTo run the full migration:');
    console.log('node src/seeders/amazonProductDataToProductSeeder.js');

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Function to test with multiple sample records
async function testWithMultipleRecords(limit = 5) {
  try {
    console.log(`=== Testing with ${limit} sample records ===\n`);

    // Get sample records
    const sampleRecords = await prisma.amazonProductData.findMany({
      take: limit,
      include: {
        company: {
          select: {
            name: true,
            website: true,
          }
        }
      }
    });

    if (sampleRecords.length === 0) {
      console.error('❌ No records found for testing');
      return false;
    }

    console.log(`Found ${sampleRecords.length} sample records`);

    // Test transformation for each record
    for (let i = 0; i < sampleRecords.length; i++) {
      const record = sampleRecords[i];
      console.log(`\nTesting record ${i + 1}/${sampleRecords.length} (ID: ${record.id})`);

      try {
        const transformed = transformAmazonDataToProduct(record);
        console.log(`✅ Transformation successful`);
        console.log(`   - URL: ${transformed.url || 'N/A'}`);
        console.log(`   - Brand: ${transformed.brand_name || 'N/A'}`);
        console.log(`   - Title: ${transformed.product_title ? transformed.product_title.substring(0, 30) + '...' : 'N/A'}`);
      } catch (error) {
        console.error(`❌ Transformation failed:`, error.message);
      }
    }

    // Test migration with these records
    console.log('\nTesting migration with sample records...');
    const recordIds = sampleRecords.map(r => r.id);
    await migrateSpecificRecords(recordIds);
    console.log('✅ Sample migration completed');

    return true;

  } catch (error) {
    console.error('❌ Multiple records test failed:', error.message);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Function to validate data structure
async function validateDataStructure() {
  try {
    console.log('=== Validating AmazonProductData Structure ===\n');

    const sampleRecord = await prisma.amazonProductData.findFirst();
    if (!sampleRecord) {
      console.error('❌ No AmazonProductData records found');
      return false;
    }

    console.log('Sample record structure:');
    console.log('- ID:', sampleRecord.id);
    console.log('- Company ID:', sampleRecord.companyId);
    console.log('- Status:', sampleRecord.status);
    console.log('- Search URL:', sampleRecord.searchUrl);
    console.log('- Slug:', sampleRecord.slug);
    console.log('- Data type:', typeof sampleRecord.data);

    if (sampleRecord.data && typeof sampleRecord.data === 'object') {
      console.log('\nData object keys:');
      Object.keys(sampleRecord.data).forEach(key => {
        console.log(`  - ${key}: ${typeof sampleRecord.data[key]}`);
      });

      // Check for productData array
      if (sampleRecord.data.productData && Array.isArray(sampleRecord.data.productData)) {
        console.log(`\nProduct data array length: ${sampleRecord.data.productData.length}`);
        if (sampleRecord.data.productData.length > 0) {
          const firstProduct = sampleRecord.data.productData[0];
          console.log('First product keys:');
          Object.keys(firstProduct).forEach(key => {
            console.log(`  - ${key}: ${typeof firstProduct[key]}`);
          });
        }
      }
    }

    console.log('\n✅ Data structure validation completed');
    return true;

  } catch (error) {
    console.error('❌ Data structure validation failed:', error.message);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--validate')) {
    validateDataStructure()
      .then((success) => {
        process.exit(success ? 0 : 1);
      })
      .catch((error) => {
        console.error('Validation failed:', error);
        process.exit(1);
      });
  } else if (args.includes('--multiple')) {
    const limit = parseInt(args[args.indexOf('--multiple') + 1]) || 5;
    testWithMultipleRecords(limit)
      .then((success) => {
        process.exit(success ? 0 : 1);
      })
      .catch((error) => {
        console.error('Multiple records test failed:', error);
        process.exit(1);
      });
  } else {
    testMigration()
      .then((success) => {
        process.exit(success ? 0 : 1);
      })
      .catch((error) => {
        console.error('Test failed:', error);
        process.exit(1);
      });
  }
}

module.exports = {
  testMigration,
  testWithMultipleRecords,
  validateDataStructure,
}; 