const express = require("express");
const prisma = require("../database/prisma/getPrismaClient");
const { addToQueue } = require("../utils/bull/bull");
const converter = require("json-2-csv");

const router = express.Router();

router.get("/api/lex/review/checker_stats", async (req, res) => {
    try {
        const { rev_id } = req.query;

        const review = await prisma.lexReview.findUnique({
            where: {
                id: parseInt(rev_id)
            },
            select: {
                id: true,
                asin: true,
                run_frequency: true,
                totalRuns: true,
                checkerStatus: true,
                removedAt: true,
                removedHistory: true,
                returnedHistory: true,
                comments: true,
            }
        });

        if (!review) {
            return res.status(404).json({
                success: false,
                error: "Review not found",
            });
        }

        res.json({
            success: true,
            data: review,
        });
    } catch (error) {
        console.error("Error in GET /api/lex/review/checker_stats:", error);
        return res.status(500).json({
            success: false,
            error: "Internal server error",
        });
    }
});

router.patch("/api/lex/review/change_run_frequency", async (req, res) => {
    try {
        const { rev_ids, asins, run_frequency } = req.body;

        // Validate required fields
        if (!rev_ids && !asins) {
            return res.status(400).json({
                success: false,
                error: "Either rev_ids or asins is required (can be single value or array)",
            });
        }

        if (run_frequency === undefined || run_frequency === null) {
            return res.status(400).json({
                success: false,
                error: "run_frequency is required",
            });
        }

        // Validate run_frequency is a number
        const frequency = parseInt(run_frequency);
        if (isNaN(frequency) || frequency < 0) {
            return res.status(400).json({
                success: false,
                error: "run_frequency must be a valid non-negative number",
            });
        }

        let validReviewIds = [];
        let asinList = [];

        // Process review IDs if provided
        if (rev_ids) {
            const reviewIds = Array.isArray(rev_ids) ? rev_ids : [rev_ids];
            validReviewIds = reviewIds.map(id => parseInt(id)).filter(id => !isNaN(id));

            if (validReviewIds.length !== reviewIds.length) {
                console.warn(`Some invalid review IDs were filtered out: ${reviewIds.filter(id => isNaN(parseInt(id))).join(', ')}`);
            }
        }

        // Process ASINs if provided
        if (asins) {
            asinList = Array.isArray(asins) ? asins : [asins];
            asinList = asinList.filter(asin => asin && typeof asin === 'string' && asin.trim() !== '');

            if (asinList.length === 0) {
                return res.status(400).json({
                    success: false,
                    error: "No valid ASINs provided",
                });
            }
        }

        // Build where clause for the update
        let whereClause = {};

        if (validReviewIds.length > 0 && asinList.length > 0) {
            // Both review IDs and ASINs provided - use OR condition
            whereClause.OR = [
                { id: { in: validReviewIds } },
                { asin: { in: asinList } }
            ];
        } else if (validReviewIds.length > 0) {
            // Only review IDs provided
            whereClause.id = { in: validReviewIds };
        } else {
            // Only ASINs provided
            whereClause.asin = { in: asinList };
        }

        // Use updateMany to update all matching reviews at once
        const updateResult = await prisma.lexReview.updateMany({
            where: whereClause,
            data: {
                run_frequency: frequency,
                updatedAt: new Date(),
            }
        });

        // Check if any records were updated
        if (updateResult.count === 0) {
            return res.status(404).json({
                success: false,
                error: "No reviews found with the provided IDs or ASINs",
            });
        }

        // Fetch some updated reviews for response (limit to 10 to avoid large responses)
        const updatedReviews = await prisma.lexReview.findMany({
            where: whereClause,
            select: {
                id: true,
                asin: true,
                run_frequency: true,
                totalRuns: true,
                checkerStatus: true,
                updatedAt: true,
            },
            orderBy: {
                id: 'asc'
            },
            take: 10
        });

        console.log(`✅ Updated ${updateResult.count} reviews run_frequency to ${frequency}`);

        res.json({
            success: true,
            message: `Run frequency updated successfully to ${frequency} for ${updateResult.count} reviews`,
            data: {
                updatedCount: updateResult.count,
                requestedReviewIds: validReviewIds.length,
                requestedAsins: asinList.length,
                frequency: frequency,
                sampleReviews: updatedReviews,
                totalRequested: validReviewIds.length + asinList.length
            },
        });
    } catch (error) {
        console.error("Error in PATCH /api/lex/review/change_run_frequency:", error);
        return res.status(500).json({
            success: false,
            error: "Internal server error",
        });
    }
});

router.post("/api/lex/review/checker/add_job", async (req, res) => {
    try {
        const { asins, rev_ids } = req.body;

        // Validate that at least one of asins or rev_ids is provided
        if ((!asins || asins.length === 0) && (!rev_ids || rev_ids.length === 0)) {
            return res.status(400).json({
                success: false,
                error: "Either asins or rev_ids must be provided",
            });
        }

        let reviewIds = [];
        let processedAsins = [];
        let processedRevIds = [];

        // If rev_ids are provided, use them directly
        if (rev_ids && rev_ids.length > 0) {
            const revIdList = Array.isArray(rev_ids) ? rev_ids : [rev_ids];
            processedRevIds = revIdList.map(id => parseInt(id)).filter(id => !isNaN(id));
            reviewIds = [...reviewIds, ...processedRevIds];
        }

        // If asins are provided, find all reviews for those ASINs
        if (asins && asins.length > 0) {
            const asinList = Array.isArray(asins) ? asins : [asins];
            processedAsins = asinList.filter(asin => asin && typeof asin === 'string' && asin.trim() !== '');
            
            if (processedAsins.length > 0) {
                const asinReviews = await prisma.lexReview.findMany({
                    where: {
                        asin: {
                            in: processedAsins
                        }
                    },
                    select: {
                        id: true,
                        asin: true
                    }
                });
                
                const asinReviewIds = asinReviews.map(review => review.id);
                reviewIds = [...new Set([...reviewIds, ...asinReviewIds])]; // Remove duplicates
            }
        }

        if (reviewIds.length === 0) {
            return res.status(404).json({
                success: false,
                error: "No reviews found for the provided ASINs or review IDs",
            });
        }

        // Create the review checker job
        const jobName = `ReviewChecker_${Date.now()}_${reviewIds.length}_reviews`;
        const reviewCheckerJob = await prisma.lexReviewCheckerJob.create({
            data: {
                name: jobName,
                status: "PENDING",
            }
        });

        // Create output data entries for each review
        const outputDataEntries = [];
        for (const reviewId of reviewIds) {
            // Get the review to get the review URL
            const review = await prisma.lexReview.findUnique({
                where: { id: reviewId },
                select: {
                    id: true,
                    reviewLink: true,
                    asin: true,
                }
            });

            if (review) {
                outputDataEntries.push({
                    revId: reviewId,
                    reviewJobId: reviewCheckerJob.id,
                    status: "PENDING",
                    reviewUrl: review.reviewLink || `https://amazon.com/review/${reviewId}`, // Fallback URL
                });
            }
        }

        // Create all output data entries
        if (outputDataEntries.length > 0) {
            await prisma.lexReviewCheckerOutputData.createMany({
                data: outputDataEntries
            });
        }

        // Add job to bull queue
        const queueJob = await addToQueue("lexReviewChecker", {
            jobId: reviewCheckerJob.id,
            reviewIds: reviewIds,
            jobName: jobName,
        }, {
            targetQueue: "lex",
        });

        console.log(`✅ Created review checker job: ${jobName} with ${reviewIds.length} reviews`);

        res.json({
            success: true,
            message: `Review checker job created successfully with ${reviewIds.length} reviews`,
            data: {
                jobId: reviewCheckerJob.id,
                jobName: jobName,
                queueJobId: queueJob.id,
                totalReviews: reviewIds.length,
                status: reviewCheckerJob.status,
                createdAt: reviewCheckerJob.createdAt,
                processedAsins: processedAsins.length,
                processedRevIds: processedRevIds.length,
                totalInputs: processedAsins.length + processedRevIds.length
            }
        });

    } catch (error) {
        console.error("Error in POST /api/lex/review/checker/add_job:", error);
        return res.status(500).json({
            success: false,
            error: "Internal server error"
        });
    }
});

// GET /api/lex/review/checker/jobs - Get all lexChecker jobs with pagination and filtering
router.get("/api/lex/review/checker/jobs", async (req, res) => {
    try {
        const {
            page = 1,
            pageSize = 20,
            status,
            sortBy = 'createdAt',
            sortOrder = 'desc',
            dateFrom,
            dateTo
        } = req.query;

        // Validate pagination parameters
        const pageNum = Math.max(1, parseInt(page));
        const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize))); // Limit to max 100 items per page
        const skip = (pageNum - 1) * pageSizeNum;

        // Validate sort parameters
        const validSortFields = ['id', 'name', 'status', 'createdAt', 'updatedAt'];
        const validSortOrders = ['asc', 'desc'];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
        const sortDirection = validSortOrders.includes(sortOrder) ? sortOrder : 'desc';

        // Build where clause
        let whereClause = {};

        if (status) {
            whereClause.status = status;
        }

        if (dateFrom || dateTo) {
            whereClause.createdAt = {};
            if (dateFrom) {
                whereClause.createdAt.gte = new Date(dateFrom);
            }
            if (dateTo) {
                whereClause.createdAt.lte = new Date(dateTo);
            }
        }

        // Get jobs with related data
        const jobs = await prisma.lexReviewCheckerJob.findMany({
            where: whereClause,
            skip,
            take: pageSizeNum,
            orderBy: { [sortField]: sortDirection },
            include: {
                _count: {
                    select: {
                        ReviewOutputData: true
                    }
                },
                ReviewOutputData: {
                    select: {
                        status: true,
                        createdAt: true
                    },
                    orderBy: {
                        createdAt: 'desc'
                    },
                    take: 5 // Get latest 5 output data entries for preview
                }
            }
        });

        // Get total count for pagination
        const totalJobs = await prisma.lexReviewCheckerJob.count({
            where: whereClause
        });

        // Calculate pagination info
        const totalPages = Math.ceil(totalJobs / pageSizeNum);
        const hasNextPage = pageNum < totalPages;
        const hasPrevPage = pageNum > 1;

        // Process jobs data for response
        const processedJobs = jobs.map(job => {
            // Calculate status breakdown
            const statusBreakdown = job.ReviewOutputData.reduce((acc, output) => {
                acc[output.status] = (acc[output.status] || 0) + 1;
                return acc;
            }, {});

            // Get latest activity
            const latestActivity = job.ReviewOutputData.length > 0
                ? job.ReviewOutputData[0].createdAt
                : job.createdAt;

            return {
                id: job.id,
                name: job.name,
                status: job.status,
                createdAt: job.createdAt,
                updatedAt: job.updatedAt,
                totalReviews: job._count.ReviewOutputData,
                statusBreakdown,
                latestActivity,
                recentOutputs: job.ReviewOutputData
            };
        });

        // Get overall review statistics for the dashboard
        const reviewStats = await prisma.lexReviewCheckerOutputData.groupBy({
            by: ['status'],
            _count: {
                id: true
            }
        });

        // Convert to simple counts
        const reviewCounts = {
            pending: 0,
            failed: 0,
            completed: 0
        };

        reviewStats.forEach(stat => {
            if (stat.status === 'PENDING') {
                reviewCounts.pending = stat._count.id;
            } else if (stat.status === 'FAILED') {
                reviewCounts.failed = stat._count.id;
            } else {
                // All other statuses (PRESENT, REMOVED, RESURRECTED) count as completed
                reviewCounts.completed += stat._count.id;
            }
        });

        res.json({
            success: true,
            data: {
                jobs: processedJobs,
                pagination: {
                    currentPage: pageNum,
                    pageSize: pageSizeNum,
                    totalJobs,
                    totalPages,
                    hasNextPage,
                    hasPrevPage
                },
                stats: {
                    pendingReviews: reviewCounts.pending,
                    failedReviews: reviewCounts.failed,
                    completedReviews: reviewCounts.completed,
                    totalReviews: reviewCounts.pending + reviewCounts.failed + reviewCounts.completed
                }
            }
        });

    } catch (error) {
        console.error("Error in GET /api/lex/review/checker/jobs:", error);
        return res.status(500).json({
            success: false,
            error: "Internal server error",
        });
    }
});

// GET /api/lex/review/checker/jobs/:jobId - Get specific job details
router.get("/api/lex/review/checker/jobs/:jobId", async (req, res) => {
    try {
        const { jobId } = req.params;

        const job = await prisma.lexReviewCheckerJob.findUnique({
            where: {
                id: parseInt(jobId)
            },
            include: {
                ReviewOutputData: {
                    include: {
                        review: {
                            select: {
                                id: true,
                                asin: true,
                                reviewID: true,
                                reviewTitle: true,
                                reviewContent: true,
                                reviewScore: true,
                                reviewer: true,
                                reviewerCountry: true,
                                reviewDate: true,
                                violation: true,
                                checkerStatus: true,
                                removedAt: true,
                                removedHistory: true,
                                returnedHistory: true,
                                comments: true
                            }
                        }
                    },
                    orderBy: {
                        createdAt: 'desc'
                    }
                },
                _count: {
                    select: {
                        ReviewOutputData: true
                    }
                }
            }
        });

        if (!job) {
            return res.status(404).json({
                success: false,
                error: "Job not found",
            });
        }

        // Calculate status breakdown
        const statusBreakdown = job.ReviewOutputData.reduce((acc, output) => {
            acc[output.status] = (acc[output.status] || 0) + 1;
            return acc;
        }, {});

        // Process output data
        const processedOutputData = job.ReviewOutputData.map(output => ({
            id: output.id,
            status: output.status,
            reviewUrl: output.reviewUrl,
            createdAt: output.createdAt,
            updatedAt: output.updatedAt,
            review: {
                id: output.review.id,
                asin: output.review.asin,
                reviewID: output.review.reviewID,
                reviewTitle: output.review.reviewTitle,
                reviewContent: output.review.reviewContent,
                reviewScore: output.review.reviewScore,
                reviewer: output.review.reviewer,
                reviewerCountry: output.review.reviewerCountry,
                reviewDate: output.review.reviewDate,
                violation: output.review.violation,
                checkerStatus: output.review.checkerStatus,
                removedAt: output.review.removedAt,
                removedHistory: output.review.removedHistory,
                returnedHistory: output.review.returnedHistory,
                comments: output.review.comments
            }
        }));

        res.json({
            success: true,
            data: {
                job: {
                    id: job.id,
                    name: job.name,
                    status: job.status,
                    createdAt: job.createdAt,
                    updatedAt: job.updatedAt,
                    totalReviews: job._count.ReviewOutputData,
                    statusBreakdown,
                    outputData: processedOutputData
                }
            }
        });

    } catch (error) {
        console.error("Error in GET /api/lex/review/checker/jobs/:jobId:", error);
        return res.status(500).json({
            success: false,
            error: "Internal server error",
        });
    }
});

// GET /api/lex/review/checker/jobs/:jobId/download - Download job data as CSV
router.get("/api/lex/review/checker/jobs/:jobId/download", async (req, res) => {
    try {
        const reviewJobId = parseInt(req.params.jobId);
        console.log("Downloading CSV for Job ID:", reviewJobId);

        // Find the Job with the given ID
        const job = await prisma.lexReviewCheckerJob.findUnique({
            where: { id: reviewJobId },
        });
        if (!job) {
            return res.status(404).json({ error: "Job not found" });
        }

        // Fetch output data including the original inputData (JSON) and new fields
        const outputData = await prisma.lexReviewCheckerOutputData.findMany({
            where: {
                reviewJobId,
            },
            include: {
                review: true,
            },
        });

        if (!outputData.length) {
            return res.status(404).json({ error: "No data found for this job" });
        }
        const finalData = outputData.map((item) => ({
            "Review ID": item.review?.reviewID || "",
            "Review URL": item.review?.reviewLink || "",
            "Review Title": item.review?.reviewTitle || "",
            Status: item.review?.checkerStatus || "",
            ASIN: item.review?.asin || "",
            "Run Frequency": item.review?.run_frequency || "",
            "Removed At": item.review?.removedAt
                ? new Date(item.review.removedAt).toLocaleDateString("en-US")
                : "",
            "Created At": item.review?.createdAt || "",
            "Updated At": item.review?.updatedAt || "",
            "Input Data": JSON.stringify(item.review) || "",
        }));
        // Convert the combined data to CSV format
        const csvContent = converter.json2csv(finalData);

        // Set the filename for the CSV file
        const analyzableCsvFileName = `lex_checker_job_${job.id}.csv`;

        // Send the CSV file in response
        res.setHeader(
            "Content-Disposition",
            `attachment; filename=${analyzableCsvFileName}`
        );
        res.setHeader("Content-Type", "text/csv");
        res.status(200).send(csvContent);
    } catch (error) {
        console.error("Error fetching output data:", error);
        res.status(500).json({ error: "Internal server error" });
    }
});

// GET /api/lex/review/checker/jobs/download/all - Download all jobs data as CSV
router.get("/api/lex/review/checker/jobs/download/all", async (req, res) => {
    try {
        const {
            status,
            dateFrom,
            dateTo,
            limit = 10000 // Limit to prevent memory issues
        } = req.query;

        // Build where clause
        let whereClause = {};

        if (status) {
            whereClause.status = status;
        }

        if (dateFrom || dateTo) {
            whereClause.createdAt = {};
            if (dateFrom) {
                whereClause.createdAt.gte = new Date(dateFrom);
            }
            if (dateTo) {
                whereClause.createdAt.lte = new Date(dateTo);
            }
        }

        const jobs = await prisma.lexReviewCheckerJob.findMany({
            where: whereClause,
            include: {
                ReviewOutputData: {
                    include: {
                        review: {
                            select: {
                                id: true,
                                asin: true,
                                reviewID: true,
                                reviewTitle: true,
                                reviewContent: true,
                                reviewScore: true,
                                reviewer: true,
                                reviewerCountry: true,
                                reviewDate: true,
                                violation: true,
                                checkerStatus: true,
                                removedAt: true,
                                removedHistory: true,
                                returnedHistory: true,
                                comments: true,
                                run_frequency: true,
                                totalRuns: true
                            }
                        }
                    }
                },
                _count: {
                    select: {
                        ReviewOutputData: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: parseInt(limit)
        });

        if (jobs.length === 0) {
            return res.status(404).json({
                success: false,
                error: "No jobs found matching the criteria",
            });
        }

        // CSV headers
        const csvHeaders = [
            'Job ID',
            'Job Name',
            'Job Status',
            'Job Created At',
            'Job Updated At',
            'Total Reviews',
            'Output ID',
            'Output Status',
            'Output Created At',
            'Output Updated At',
            'Review URL',
            'Review ID',
            'ASIN',
            'Review Title',
            'Review Content',
            'Review Score',
            'Reviewer',
            'Reviewer Country',
            'Review Date',
            'Violation',
            'Checker Status',
            'Removed At',
            'Removed History',
            'Returned History',
            'Comments',
            'Run Frequency',
            'Total Runs'
        ];

        // Process data for CSV
        const csvRows = [];
        let totalRows = 0;

        jobs.forEach(job => {
            job.ReviewOutputData.forEach(output => {
                const review = output.review;

                csvRows.push([
                    job.id,
                    `"${job.name.replace(/"/g, '""')}"`,
                    job.status,
                    job.createdAt.toISOString(),
                    job.updatedAt.toISOString(),
                    job._count.ReviewOutputData,
                    output.id,
                    output.status,
                    output.createdAt.toISOString(),
                    output.updatedAt.toISOString(),
                    `"${output.reviewUrl.replace(/"/g, '""')}"`,
                    review.reviewID || '',
                    review.asin || '',
                    `"${(review.reviewTitle || '').replace(/"/g, '""')}"`,
                    `"${(review.reviewContent || '').replace(/"/g, '""')}"`,
                    review.reviewScore || '',
                    `"${(review.reviewer || '').replace(/"/g, '""')}"`,
                    review.reviewerCountry || '',
                    review.reviewDate ? new Date(review.reviewDate).toISOString() : '',
                    review.violation || false,
                    review.checkerStatus || '',
                    review.removedAt ? review.removedAt.toISOString() : '',
                    `"${(JSON.stringify(review.removedHistory) || '').replace(/"/g, '""')}"`,
                    `"${(JSON.stringify(review.returnedHistory) || '').replace(/"/g, '""')}"`,
                    `"${(review.comments || '').replace(/"/g, '""')}"`,
                    review.run_frequency || 0,
                    review.totalRuns || 0
                ]);
                totalRows++;
            });
        });

        const csvContent = [
            csvHeaders.join(','),
            ...csvRows.map(row => row.join(','))
        ].join('\n');

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').slice(0, -5);
        const filename = `all_lex_checker_jobs_${timestamp}_${jobs.length}_jobs_${totalRows}_items.csv`;

        console.log(`📥 Downloading all lexChecker jobs: ${jobs.length} jobs, ${totalRows} total items`);

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Length', Buffer.byteLength(csvContent));

        return res.status(200).send(csvContent);

    } catch (error) {
        console.error("Error in GET /api/lex/review/checker/jobs/download/all:", error);
        return res.status(500).json({
            success: false,
            error: "Internal server error",
        });
    }
});

module.exports = router;